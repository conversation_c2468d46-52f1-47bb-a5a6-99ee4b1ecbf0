// src/app/page.tsx

'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { formatAlpacaOptionSymbol } from '../utils/formatAlpacaOptionSymbol';
import { fetchHistoricalOptionBars } from '@/utils/fetchHistoricalOptionBars';
import { fetchHistoricalStockBars } from '@/utils/fetchHistoricalStockBars';
import { transformAlpacaDataToChart, validateChartData } from '@/utils/transformChartData';
import TradingViewChart, { TradingViewChartRef } from '@/components/TradingViewChart';
import { Time } from 'lightweight-charts';
import AutosuggestInput from '@/components/AutosuggestInput';
import tickersData from '@/data/tickers.json';

export default function HomePage() {
  // Form toggle state
  const [formType, setFormType] = useState<'options' | 'stocks'>('options');

  // Options form state
  const [symbol, setSymbol] = useState('');
  const [expiration, setExpiration] = useState('');
  const [type, setType] = useState<'C' | 'P'>('C');
  const [strike, setStrike] = useState('');
  const [timeframe, setTimeframe] = useState('1Min');
  const [startDate, setStartDate] = useState(() => {
    // Default to current date
    const today = new Date();
    return today.toISOString().split('T')[0];
  });
  const [endDate, setEndDate] = useState(() => {
    // Default to current date (will be updated when expiration is set)
    const today = new Date();
    return today.toISOString().split('T')[0];
  });
  const [hasUserChangedDateRange, setHasUserChangedDateRange] = useState(false);

  // Stock form state
  const [stockSymbol, setStockSymbol] = useState('');
  const [stockDate, setStockDate] = useState(() => {
    // Default to current date
    const today = new Date();
    return today.toISOString().split('T')[0];
  });

  // Common state
  const [isFormMinimized, setIsFormMinimized] = useState(false);
  const [hasDataBeenFetched, setHasDataBeenFetched] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const chartRef = useRef<TradingViewChartRef>(null);

  // Risk Management Scenario type
  interface RiskScenario {
    id: string;
    takeProfitOffset: number;
    stopLossOffset: number;
    takeProfitAchieved: boolean;
  }

  // Option analysis results state - now supports multiple analyses
  const [optionAnalysisResults, setOptionAnalysisResults] = useState<Array<{
    id: string;
    strikePrice: number;
    optionOpenPrice: number;
    breakoutBarTime: string;
    optionMFE: number;
    optionClosePrice: number;
    riskScenarios: RiskScenario[];
    breakoutNumber: number;
  }>>([]);

  // Put option analysis results state - now supports multiple analyses
  const [putOptionAnalysisResults, setPutOptionAnalysisResults] = useState<Array<{
    id: string;
    strikePrice: number;
    optionOpenPrice: number;
    breakoutBarTime: string;
    optionMFE: number;
    optionClosePrice: number;
    riskScenarios: RiskScenario[];
    breakoutNumber: number;
  }>>([]);

  // Dynamic risk scenarios array
  const [riskScenarios, setRiskScenarios] = useState<RiskScenario[]>([
    { id: '1', takeProfitOffset: 0.25, stopLossOffset: 0.50, takeProfitAchieved: false },
    { id: '2', takeProfitOffset: 0.50, stopLossOffset: 0.75, takeProfitAchieved: false },
    { id: '3', takeProfitOffset: 0.75, stopLossOffset: 1.00, takeProfitAchieved: false },
    { id: '4', takeProfitOffset: 1.00, stopLossOffset: 1.25, takeProfitAchieved: false }
  ]);

  // Store option chart data for recalculation
  const [lastOptionChartData, setLastOptionChartData] = useState<unknown[]>([]);
  const [lastMatchingBar, setLastMatchingBar] = useState<unknown>(null);
  const [lastBreakoutTime, setLastBreakoutTime] = useState<unknown>(null);

  // Store put option chart data for recalculation
  const [lastPutOptionChartData, setLastPutOptionChartData] = useState<unknown[]>([]);
  const [lastPutMatchingBar, setLastPutMatchingBar] = useState<unknown>(null);
  const [lastPutBreakoutTime, setLastPutBreakoutTime] = useState<unknown>(null);

  // Functions to manage risk scenarios
  const addRiskScenario = () => {
    const newId = (riskScenarios.length + 1).toString();
    const newScenario: RiskScenario = {
      id: newId,
      takeProfitOffset: 0.25,
      stopLossOffset: 0.50,
      takeProfitAchieved: false
    };
    setRiskScenarios([...riskScenarios, newScenario]);
  };

  const removeRiskScenario = (id: string) => {
    if (riskScenarios.length > 1) { // Keep at least one scenario
      setRiskScenarios(riskScenarios.filter(scenario => scenario.id !== id));
    }
  };

  const updateRiskScenario = (id: string, field: 'takeProfitOffset' | 'stopLossOffset', value: number) => {
    setRiskScenarios(riskScenarios.map(scenario =>
      scenario.id === id ? { ...scenario, [field]: value } : scenario
    ));
  };

  const formatted = formatAlpacaOptionSymbol(
    symbol,
    expiration,
    type,
    parseFloat(strike)
  );

  // Auto-update both start and end dates when expiration changes (unless user has manually changed them)
  useEffect(() => {
    if (expiration && !hasUserChangedDateRange) {
      setStartDate(expiration);
      setEndDate(expiration);
    }
  }, [expiration, hasUserChangedDateRange]);

  // Trigger chart resize when form minimize state changes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (chartRef.current) {
        chartRef.current.resize();
      }
    }, 350); // Wait for CSS transition to complete

    return () => clearTimeout(timer);
  }, [isFormMinimized]);

  // Calculate option metrics (MFE, close price, and take profit achieved for dynamic scenarios)
  const calculateOptionMetrics = useCallback((
    optionChartData: unknown[],
    matchingBar: unknown,
    breakoutTime: unknown,
    scenarios: RiskScenario[]
  ): { mfe: number; closePrice: number; riskResults: RiskScenario[] } => {
    // Type guards for data validation
    const isValidBar = (bar: unknown): bar is { time: number; high: number; low: number; close: number } => {
      return typeof bar === 'object' && bar !== null &&
             'time' in bar && 'high' in bar && 'low' in bar && 'close' in bar;
    };

    const isValidMatchingBar = (bar: unknown): bar is { open: number } => {
      return typeof bar === 'object' && bar !== null && 'open' in bar;
    };

    // Validate inputs
    if (!Array.isArray(optionChartData) || !isValidMatchingBar(matchingBar) || typeof breakoutTime !== 'number') {
      console.error('Invalid input data for option metrics calculation');
      return { mfe: 0, closePrice: 0, riskResults: scenarios.map(s => ({ ...s, takeProfitAchieved: false })) };
    }

    const validChartData = optionChartData.filter(isValidBar);

    // Find all bars after the matching bar (breakout time)
    const barsAfterBreakout = validChartData.filter(bar => Number(bar.time) > Number(breakoutTime));

    // Calculate MFE (Maximum Favorable Excursion) - highest price after the open
    let mfe = matchingBar.open; // Start with the open price
    if (barsAfterBreakout.length > 0) {
      const highestHigh = Math.max(...barsAfterBreakout.map(bar => bar.high));
      mfe = Math.max(mfe, highestHigh);
    }

    // Get the close price of the last candle of the day
    const lastBar = validChartData[validChartData.length - 1];
    const closePrice = lastBar ? lastBar.close : matchingBar.open;

    // Calculate take profit and stop loss prices for all scenarios
    const scenarioCalculations = scenarios.map(scenario => ({
      ...scenario,
      takeProfitPrice: matchingBar.open + scenario.takeProfitOffset,
      stopLossPrice: matchingBar.open - scenario.stopLossOffset
    }));

    // Calculate take profit achieved for each scenario
    const riskResults = scenarioCalculations.map(scenario => {
      let takeProfitAchieved = false;

      // Check each bar after the breakout in chronological order
      for (const bar of barsAfterBreakout) {
        // Check if stop loss was hit first (price went below stop loss)
        if (bar.low <= scenario.stopLossPrice) {
          takeProfitAchieved = false;
          break; // Stop loss hit, exit early
        }

        // Check if take profit was hit (price went above take profit)
        if (bar.high >= scenario.takeProfitPrice) {
          takeProfitAchieved = true;
          break; // Take profit hit, exit early
        }
      }

      return {
        id: scenario.id,
        takeProfitOffset: scenario.takeProfitOffset,
        stopLossOffset: scenario.stopLossOffset,
        takeProfitAchieved: takeProfitAchieved
      };
    });

    console.log('Option metrics calculated:', {
      openPrice: matchingBar.open,
      mfe,
      closePrice,
      scenarioCalculations,
      riskResults,
      barsAfterBreakout: barsAfterBreakout.length
    });

    return {
      mfe,
      closePrice,
      riskResults
    };
  }, []); // Empty dependency array since this function doesn't depend on any props or state

  // Recalculate option analyses when risk scenarios change
  useEffect(() => {
    // Recalculate call option analyses
    if (optionAnalysisResults.length > 0 && lastOptionChartData.length > 0 && lastMatchingBar && lastBreakoutTime) {
      const { mfe, closePrice, riskResults } = calculateOptionMetrics(
        lastOptionChartData,
        lastMatchingBar,
        lastBreakoutTime,
        riskScenarios
      );

      setOptionAnalysisResults(prev => prev.map((result, index) =>
        index === prev.length - 1 ? {
          ...result,
          optionMFE: mfe,
          optionClosePrice: closePrice,
          riskScenarios: riskResults
        } : result
      ));
    }

    // Recalculate put option analyses
    if (putOptionAnalysisResults.length > 0 && lastPutOptionChartData.length > 0 && lastPutMatchingBar && lastPutBreakoutTime) {
      const { mfe, closePrice, riskResults } = calculateOptionMetrics(
        lastPutOptionChartData,
        lastPutMatchingBar,
        lastPutBreakoutTime,
        riskScenarios
      );

      setPutOptionAnalysisResults(prev => prev.map((result, index) =>
        index === prev.length - 1 ? {
          ...result,
          optionMFE: mfe,
          optionClosePrice: closePrice,
          riskScenarios: riskResults
        } : result
      ));
    }
  }, [riskScenarios]); // Only depend on riskScenarios to avoid infinite loops

  // Function to perform option analysis
  async function performOptionAnalysis(
    symbol: string,
    date: string,
    strikePrice: number,
    breakoutBar: { time: Time; open: number },
    breakoutNumber: number = 1
  ) {
    try {
      console.log('Performing option analysis:', { symbol, date, strikePrice, breakoutBar });

      // Create option symbol (format: SYMBOL + YYMMDD + C + strike price with 8 digits)
      // Parse date string directly to avoid timezone issues
      const [yearFull, monthStr, dayStr] = date.split('-');
      const year = yearFull.slice(-2);
      const month = monthStr;
      const day = dayStr;
      const strikeFormatted = (strikePrice * 1000).toString().padStart(8, '0');
      const optionSymbol = `${symbol}${year}${month}${day}C${strikeFormatted}`;

      console.log('Generated option symbol:', optionSymbol);

      // Make API call for option data
      // Determine the correct timezone offset for the given date
      const dateObj = new Date(date);
      const monthNum = dateObj.getMonth() + 1; // getMonth() returns 0-11, we want 1-12
      const isEDT = monthNum >= 3 && monthNum <= 10; // March through October
      const timezoneOffset = isEDT ? '-04:00' : '-05:00';

      const startTime = encodeURIComponent(`${date}T09:30:00${timezoneOffset}`);
      const endTime = encodeURIComponent(`${date}T16:00:00${timezoneOffset}`);
      const response = await fetch(`/api/historical-option-bars?symbol=${optionSymbol}&start=${startTime}&end=${endTime}&timeframe=5Min`);

      if (!response.ok) {
        throw new Error(`Failed to fetch option data: ${response.statusText}`);
      }

      const optionData = await response.json();
      console.log('Option data received:', optionData);

      if (!optionData.bars || optionData.bars.length === 0) {
        console.log('No option data available for this strike price and date');
        return;
      }

      // Transform option data to chart format
      const optionChartData = transformAlpacaDataToChart(optionData);

      // Check if transformation resulted in valid data
      if (!optionChartData || optionChartData.length === 0) {
        console.log('No valid option chart data after transformation');
        return;
      }

      console.log('Transformed option chart data:', optionChartData.length, 'bars');

      // Find the option candlestick that matches the breakout bar time
      const breakoutTime = breakoutBar.time;
      console.log('Looking for option bar matching time:', breakoutTime);
      console.log('Available option times:', optionChartData.map(bar => bar.time));

      const matchingOptionBar = optionChartData.find(bar => bar.time === breakoutTime);

      if (matchingOptionBar) {
        console.log('Found matching option bar:', matchingOptionBar);

        // Calculate additional metrics
        const { mfe, closePrice, riskResults } = calculateOptionMetrics(
          optionChartData,
          matchingOptionBar,
          breakoutTime,
          riskScenarios
        );

        // Store data for recalculation
        setLastOptionChartData(optionChartData);
        setLastMatchingBar(matchingOptionBar);
        setLastBreakoutTime(breakoutTime);

        // Add the analysis results to the array
        const newResult = {
          id: `call-${Date.now()}-${breakoutNumber}`,
          strikePrice: strikePrice,
          optionOpenPrice: matchingOptionBar.open,
          breakoutBarTime: new Date(Number(breakoutTime) * 1000).toLocaleTimeString('en-US', {
            timeZone: 'America/New_York',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }),
          optionMFE: mfe,
          optionClosePrice: closePrice,
          riskScenarios: riskResults,
          breakoutNumber: breakoutNumber
        };

        setOptionAnalysisResults(prev => [...prev, newResult]);
      } else {
        console.log('No matching option bar found for breakout time:', breakoutTime);

        // Check if we have any option data to work with
        if (optionChartData.length === 0) {
          console.log('No option data available for analysis');
          return;
        }

        console.log('Trying to find closest option bar...');

        // If exact match not found, try to find the closest time
        const closestBar = optionChartData.reduce((closest, current) => {
          const currentDiff = Math.abs(Number(current.time) - Number(breakoutTime));
          const closestDiff = Math.abs(Number(closest.time) - Number(breakoutTime));
          return currentDiff < closestDiff ? current : closest;
        });

        if (closestBar && Math.abs(Number(closestBar.time) - Number(breakoutTime)) <= 300) { // Within 5 minutes
          console.log('Found closest option bar within 5 minutes:', closestBar);

          // Calculate additional metrics
          const { mfe, closePrice, riskResults } = calculateOptionMetrics(
            optionChartData,
            closestBar,
            breakoutTime,
            riskScenarios
          );

          // Store data for recalculation
          setLastOptionChartData(optionChartData);
          setLastMatchingBar(closestBar);
          setLastBreakoutTime(breakoutTime);

          // Add the analysis results to the array
          const newResult = {
            id: `call-${Date.now()}-${breakoutNumber}`,
            strikePrice: strikePrice,
            optionOpenPrice: closestBar.open,
            breakoutBarTime: new Date(Number(breakoutTime) * 1000).toLocaleTimeString('en-US', {
              timeZone: 'America/New_York',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            }),
            optionMFE: mfe,
            optionClosePrice: closePrice,
            riskScenarios: riskResults,
            breakoutNumber: breakoutNumber
          };

          setOptionAnalysisResults(prev => [...prev, newResult]);
        }
      }

    } catch (error) {
      console.error('Error in option analysis:', error);
    }
  }

  // Function to perform put option analysis
  async function performPutOptionAnalysis(
    symbol: string,
    date: string,
    strikePrice: number,
    breakoutBar: { time: Time; open: number },
    breakoutNumber: number = 1
  ) {
    try {
      console.log('Performing put option analysis:', { symbol, date, strikePrice, breakoutBar });

      // Create put option symbol (format: SYMBOL + YYMMDD + P + strike price with 8 digits)
      // Parse date string directly to avoid timezone issues
      const [yearFull, monthStr, dayStr] = date.split('-');
      const year = yearFull.slice(-2);
      const month = monthStr;
      const day = dayStr;
      const strikeFormatted = (strikePrice * 1000).toString().padStart(8, '0');
      const putOptionSymbol = `${symbol}${year}${month}${day}P${strikeFormatted}`;

      console.log('Generated put option symbol:', putOptionSymbol);

      // Make API call for put option data
      // Determine the correct timezone offset for the given date
      const dateObj = new Date(date);
      const monthNum = dateObj.getMonth() + 1; // getMonth() returns 0-11, we want 1-12
      const isEDT = monthNum >= 3 && monthNum <= 10; // March through October
      const timezoneOffset = isEDT ? '-04:00' : '-05:00';

      const startTime = encodeURIComponent(`${date}T09:30:00${timezoneOffset}`);
      const endTime = encodeURIComponent(`${date}T16:00:00${timezoneOffset}`);

      console.log('Fetching put option data with params:', {
        symbol: putOptionSymbol,
        start: startTime,
        end: endTime,
        timeframe: '5Min'
      });

      const response = await fetch(`/api/historical-option-bars?symbol=${putOptionSymbol}&start=${startTime}&end=${endTime}&timeframe=5Min`);

      if (!response.ok) {
        throw new Error(`Failed to fetch put option data: ${response.statusText}`);
      }

      const putOptionData = await response.json();
      console.log('Raw put option API response:', putOptionData);

      if (!putOptionData.bars || putOptionData.bars.length === 0) {
        console.log('No put option data available for this strike price and date');
        return;
      }

      // Transform the data for the chart
      const putOptionChartData = transformAlpacaDataToChart(putOptionData);

      console.log('Transformed put option chart data:', putOptionChartData.length, 'bars');

      // Find the put option candlestick that matches the breakout bar time
      const breakoutTime = breakoutBar.time;
      console.log('Looking for put option bar matching time:', breakoutTime);
      console.log('Available put option times:', putOptionChartData.map(bar => bar.time));

      const matchingPutOptionBar = putOptionChartData.find(bar => bar.time === breakoutTime);

      if (matchingPutOptionBar) {
        console.log('Found matching put option bar:', matchingPutOptionBar);

        // Calculate additional metrics
        const { mfe, closePrice, riskResults } = calculateOptionMetrics(
          putOptionChartData,
          matchingPutOptionBar,
          breakoutTime,
          riskScenarios
        );

        // Store data for recalculation
        setLastPutOptionChartData(putOptionChartData);
        setLastPutMatchingBar(matchingPutOptionBar);
        setLastPutBreakoutTime(breakoutTime);

        // Add the analysis results to the array
        const newResult = {
          id: `put-${Date.now()}-${breakoutNumber}`,
          strikePrice: strikePrice,
          optionOpenPrice: matchingPutOptionBar.open,
          breakoutBarTime: new Date(Number(breakoutTime) * 1000).toLocaleTimeString('en-US', {
            timeZone: 'America/New_York',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }),
          optionMFE: mfe,
          optionClosePrice: closePrice,
          riskScenarios: riskResults,
          breakoutNumber: breakoutNumber
        };

        setPutOptionAnalysisResults(prev => [...prev, newResult]);
      } else {
        console.log('No matching put option bar found for breakout time:', breakoutTime);

        // Check if we have any put option data to work with
        if (putOptionChartData.length === 0) {
          console.log('No put option data available for analysis');
          return;
        }

        console.log('Trying to find closest put option bar...');

        // If exact match not found, try to find the closest time
        const closestBar = putOptionChartData.reduce((closest, current) => {
          const currentDiff = Math.abs(Number(current.time) - Number(breakoutTime));
          const closestDiff = Math.abs(Number(closest.time) - Number(breakoutTime));
          return currentDiff < closestDiff ? current : closest;
        });

        if (closestBar && Math.abs(Number(closestBar.time) - Number(breakoutTime)) <= 300) { // Within 5 minutes
          console.log('Found closest put option bar within 5 minutes:', closestBar);

          // Calculate additional metrics
          const { mfe, closePrice, riskResults } = calculateOptionMetrics(
            putOptionChartData,
            closestBar,
            breakoutTime,
            riskScenarios
          );

          // Store data for recalculation
          setLastPutOptionChartData(putOptionChartData);
          setLastPutMatchingBar(closestBar);
          setLastPutBreakoutTime(breakoutTime);

          // Add the analysis results to the array
          const newResult = {
            id: `put-${Date.now()}-${breakoutNumber}`,
            strikePrice: strikePrice,
            optionOpenPrice: closestBar.open,
            breakoutBarTime: new Date(Number(breakoutTime) * 1000).toLocaleTimeString('en-US', {
              timeZone: 'America/New_York',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            }),
            optionMFE: mfe,
            optionClosePrice: closePrice,
            riskScenarios: riskResults,
            breakoutNumber: breakoutNumber
          };

          setPutOptionAnalysisResults(prev => [...prev, newResult]);
        }
      }

    } catch (error) {
      console.error('Error in put option analysis:', error);
    }
  }

  // Function to check if all risk scenarios are stopped out
  function areAllScenariosStoppedOut(riskResults: RiskScenario[]): boolean {
    return riskResults.every(scenario => !scenario.takeProfitAchieved);
  }

  // Function to analyze multiple breakouts throughout the day
  async function analyzeMultipleBreakouts(
    chartData: any[],
    highestHigh: number,
    lowestLow: number,
    stockSymbol: string,
    stockDate: string
  ) {
    let callBreakoutNumber = 1;
    let putBreakoutNumber = 1;
    let lastCallAnalysisTime: number | null = null;
    let lastPutAnalysisTime: number | null = null;

    // Process bars in chronological order
    for (const bar of chartData) {
      // Check for call option breakouts (above high line)
      if (bar.open > highestHigh) {
        // If this is the first breakout, or if all previous scenarios were stopped out
        const shouldAnalyzeCall = lastCallAnalysisTime === null ||
          (optionAnalysisResults.length > 0 &&
           areAllScenariosStoppedOut(optionAnalysisResults[optionAnalysisResults.length - 1].riskScenarios));

        if (shouldAnalyzeCall) {
          console.log(`Found call breakout #${callBreakoutNumber}:`, bar);

          // Calculate strike price (round down to nearest whole number)
          const strikePrice = Math.floor(bar.open);

          // Perform call option analysis
          await performOptionAnalysis(stockSymbol, stockDate, strikePrice, bar, callBreakoutNumber);

          lastCallAnalysisTime = Number(bar.time);
          callBreakoutNumber++;
        }
      }

      // Check for put option breakouts (below low line)
      if (bar.open < lowestLow) {
        // If this is the first breakout, or if all previous scenarios were stopped out
        const shouldAnalyzePut = lastPutAnalysisTime === null ||
          (putOptionAnalysisResults.length > 0 &&
           areAllScenariosStoppedOut(putOptionAnalysisResults[putOptionAnalysisResults.length - 1].riskScenarios));

        if (shouldAnalyzePut) {
          console.log(`Found put breakout #${putBreakoutNumber}:`, bar);

          // Calculate strike price (round up to nearest whole number for puts)
          const putStrikePrice = Math.ceil(bar.open);

          // Perform put option analysis
          await performPutOptionAnalysis(stockSymbol, stockDate, putStrikePrice, bar, putBreakoutNumber);

          lastPutAnalysisTime = Number(bar.time);
          putBreakoutNumber++;
        }
      }
    }

    console.log(`Analysis complete. Found ${callBreakoutNumber - 1} call breakouts and ${putBreakoutNumber - 1} put breakouts.`);
  }

  async function handleFetch() {
    try {
      setIsLoading(true);
      setError(null);
      setOptionAnalysisResults([]);
      setPutOptionAnalysisResults([]);

      // Clear existing chart data first
      if (chartRef.current) {
        chartRef.current.clearData();
      }

      let alpacaData;

      if (formType === 'options') {
        alpacaData = await fetchHistoricalOptionBars(formatted, startDate, endDate, timeframe);
      } else {
        // For stocks, use the stock date for both start and end
        alpacaData = await fetchHistoricalStockBars(stockSymbol, stockDate, stockDate, '5Min');
      }

      console.log('Raw Alpaca data:', alpacaData);

      // Transform the Alpaca data into chart-compatible format
      const chartData = transformAlpacaDataToChart(alpacaData);
      console.log('Transformed chart data:', chartData);

      // Validate the data before updating the chart
      if (validateChartData(chartData)) {
        // Update the chart with the new data
        if (chartRef.current) {
          chartRef.current.updateData(chartData);

          // For stock data (5min charts), add price lines for first 3 bars
          if (formType === 'stocks' && chartData.length >= 3) {
            const firstThreeBars = chartData.slice(0, 3);
            const highestHigh = Math.max(...firstThreeBars.map(bar => bar.high));
            const lowestLow = Math.min(...firstThreeBars.map(bar => bar.low));

            console.log('First 3 bars high/low:', { highestHigh, lowestLow, firstThreeBars });

            // Add the price lines
            chartRef.current.addPriceLines(highestHigh, lowestLow);

            // Find ALL breakout bars throughout the day and analyze each one
            await analyzeMultipleBreakouts(chartData, highestHigh, lowestLow, stockSymbol, stockDate);
          }
        }
        setHasDataBeenFetched(true);
      } else {
        throw new Error('Invalid chart data received - no valid data points found');
      }
    } catch (error) {
      console.error('Fetch error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch chart data';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }


  return (
    <main className="h-screen w-full bg-gray-900 flex flex-col lg:flex-row overflow-hidden">
      {/* TradingView Chart - Top on mobile, Left 3/4 on desktop */}
      <div className={`flex-1 ${isFormMinimized ? 'h-full' : 'h-64 sm:h-80 md:h-96'} lg:h-full lg:w-3/4 relative transition-all duration-300 ease-in-out`}>
        <TradingViewChart ref={chartRef} className="w-full h-full" />

        {/* Chart Overlay Messages */}
        {!hasDataBeenFetched && !isLoading && !error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-90 z-10">
            <div className="text-center text-white p-6">
              <div className="text-4xl mb-4">📊</div>
              <h2 className="text-xl sm:text-2xl font-semibold mb-2">Fill Out Form to Fetch Chart</h2>
              <p className="text-gray-300 text-sm sm:text-base">
                Enter option details in the form and click &quot;Fetch Historical Bars&quot; to view chart data
              </p>
            </div>
          </div>
        )}

        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-90 z-10">
            <div className="text-center text-white p-6">
              <div className="animate-spin text-4xl mb-4">⏳</div>
              <h2 className="text-xl sm:text-2xl font-semibold mb-2">Loading Chart Data...</h2>
              <p className="text-gray-300 text-sm sm:text-base">
                Fetching historical option bars from Alpaca
              </p>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-90 z-10">
            <div className="text-center text-white p-6 max-w-md mx-auto">
              <div className="text-4xl mb-4">❌</div>
              <h2 className="text-xl sm:text-2xl font-semibold mb-2 text-red-400">Error Loading Chart</h2>
              <p className="text-gray-300 text-sm sm:text-base mb-4">
                {error}
              </p>
              <button
                type="button"
                onClick={() => setError(null)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm"
              >
                Dismiss
              </button>
            </div>
          </div>
        )}

        {/* Mobile Toggle Button - Only visible on mobile when form is minimized */}
        {isFormMinimized && (
          <button
            type="button"
            onClick={() => {
              setIsFormMinimized(false);
              // Trigger resize after a short delay to allow DOM update
              setTimeout(() => {
                if (chartRef.current) {
                  chartRef.current.resize();
                }
              }, 100);
            }}
            className="absolute bottom-4 right-4 lg:hidden bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 hover:scale-110 transition-all duration-200 z-10 animate-in fade-in zoom-in duration-300"
            aria-label="Show form"
            title="Show option form"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
            </svg>
          </button>
        )}
      </div>

      {/* Thin separator border - horizontal on mobile, vertical on desktop */}
      {!isFormMinimized && <div className="h-px lg:h-full lg:w-px bg-gray-600"></div>}

      {/* Alpaca Option Symbol Generator Form - Bottom on mobile, Right 1/4 on desktop */}
      {!isFormMinimized && (
        <div className="w-full lg:w-1/4 h-auto lg:h-full bg-white overflow-y-auto animate-in slide-in-from-bottom duration-300 lg:animate-none">
          <div className="p-4 sm:p-6 space-y-4 sm:space-y-6 relative">
            {/* Mobile Minimize Button - Only visible on mobile */}
            <button
              type="button"
              onClick={() => {
                setIsFormMinimized(true);
                // Trigger resize after a short delay to allow DOM update
                setTimeout(() => {
                  if (chartRef.current) {
                    chartRef.current.resize();
                  }
                }, 100);
              }}
              className="absolute top-2 right-2 lg:hidden bg-gray-200 text-gray-600 p-2 rounded-full hover:bg-gray-300 hover:scale-110 transition-all duration-200 animate-pulse"
              aria-label="Minimize form"
              title="Minimize form to view chart fullscreen"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </button>
          <h1 className="text-xl sm:text-2xl font-bold text-center text-gray-800">
            {formType === 'options' ? '🧮 Option Historical Charts' : '📊 5min Historical Charts'}
          </h1>

          {/* Form Toggle Switch */}
          <div className="flex items-center justify-center space-x-4 py-2">
            <span className={`text-sm font-medium ${formType === 'options' ? 'text-blue-600' : 'text-gray-500'}`}>
              Options
            </span>
            <button
              type="button"
              onClick={() => {
                setFormType(formType === 'options' ? 'stocks' : 'options');
                setError(null);
                setHasDataBeenFetched(false);
                setOptionAnalysisResults([]);
                setPutOptionAnalysisResults([]);
                if (chartRef.current) {
                  chartRef.current.clearData();
                }
              }}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                formType === 'stocks' ? 'bg-blue-600' : 'bg-gray-200'
              }`}
              aria-label={`Switch to ${formType === 'options' ? 'stocks' : 'options'} form`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  formType === 'stocks' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm font-medium ${formType === 'stocks' ? 'text-blue-600' : 'text-gray-500'}`}>
              Stocks
            </span>
          </div>

          <div className="space-y-3 sm:space-y-4">
            {formType === 'options' ? (
              <>
                {/* Symbol */}
                <div>
                  <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-1">
                    Underlying Symbol
                  </label>
                  <AutosuggestInput
                    id="symbol"
                    value={symbol}
                    onChange={setSymbol}
                    suggestions={tickersData}
                    placeholder="e.g. AAPL"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                    maxSuggestions={8}
                  />
                </div>

            {/* Expiration */}
            <div>
              <label htmlFor="expiration" className="block text-sm font-medium text-gray-700 mb-1">
                Expiration Date
              </label>
              <input
                id="expiration"
                type="date"
                value={expiration}
                onChange={(e) => setExpiration(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
              />
            </div>

            {/* Option Type */}
            <div>
              <label htmlFor="option-type" className="block text-sm font-medium text-gray-700 mb-1">
                Option Type
              </label>
              <select
                id="option-type"
                value={type}
                onChange={(e) => setType(e.target.value as 'C' | 'P')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base bg-white"
              >
                <option value="C">Call</option>
                <option value="P">Put</option>
              </select>
            </div>

            {/* Strike Price */}
            <div>
              <label htmlFor="strike" className="block text-sm font-medium text-gray-700 mb-1">
                Strike Price
              </label>
              <input
                id="strike"
                type="number"
                step="0.01"
                value={strike}
                onChange={(e) => setStrike(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                placeholder="e.g. 150.00"
              />
            </div>

            {/* Timeframe */}
            <div>
              <label htmlFor="timeframe" className="block text-sm font-medium text-gray-700 mb-1">
                Timeframe
              </label>
              <select
                id="timeframe"
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base bg-white"
              >
                <option value="1Min">1 Minute</option>
                <option value="5Min">5 Minutes</option>
                <option value="15Min">15 Minutes</option>
                <option value="30Min">30 Minutes</option>
                <option value="1Hour">1 Hour</option>
                <option value="4Hour">4 Hour</option>
                <option value="1Day">1 Day</option>
                <option value="1Week">1 Week</option>
                <option value="1Month">1 Month</option>
              </select>
            </div>

            {/* Chart Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Chart Date Range
              </label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label htmlFor="start-date" className="block text-xs text-gray-500 mb-1">
                    Start Date
                  </label>
                  <input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => {
                      setStartDate(e.target.value);
                      // Mark that user has manually changed the date range
                      setHasUserChangedDateRange(true);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                  />
                </div>
                <div>
                  <label htmlFor="end-date" className="block text-xs text-gray-500 mb-1">
                    End Date
                  </label>
                  <input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => {
                      setEndDate(e.target.value);
                      // Mark that user has manually changed the date range
                      setHasUserChangedDateRange(true);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                  />
                </div>
              </div>
            </div>
              </>
            ) : (
              <>
                {/* Stock Symbol */}
                <div>
                  <label htmlFor="stock-symbol" className="block text-sm font-medium text-gray-700 mb-1">
                    Symbol
                  </label>
                  <AutosuggestInput
                    id="stock-symbol"
                    value={stockSymbol}
                    onChange={setStockSymbol}
                    suggestions={tickersData}
                    placeholder="e.g. SPY"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                    maxSuggestions={8}
                  />
                </div>

                {/* Date */}
                <div>
                  <label htmlFor="stock-date" className="block text-sm font-medium text-gray-700 mb-1">
                    Date
                  </label>
                  <input
                    id="stock-date"
                    type="date"
                    value={stockDate}
                    onChange={(e) => setStockDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                  />
                </div>

                {/* Dynamic Risk Management Scenarios */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-medium text-gray-700">Risk Management Scenarios</h3>
                    <button
                      type="button"
                      onClick={addRiskScenario}
                      className="flex items-center gap-1 px-2 py-1 text-xs bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                    >
                      <span className="text-sm font-bold">+</span>
                      Add Scenario
                    </button>
                  </div>

                  {riskScenarios.map((scenario, index) => (
                    <div key={scenario.id} className="relative">
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label htmlFor={`take-profit-${scenario.id}`} className="block text-xs font-medium text-gray-600 mb-1">
                            Scenario {index + 1}: Take Profit (+$)
                          </label>
                          <input
                            id={`take-profit-${scenario.id}`}
                            type="number"
                            step="0.01"
                            min="0.01"
                            value={scenario.takeProfitOffset}
                            onChange={(e) => updateRiskScenario(scenario.id, 'takeProfitOffset', parseFloat(e.target.value) || 0.25)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                          />
                        </div>
                        <div>
                          <label htmlFor={`stop-loss-${scenario.id}`} className="block text-xs font-medium text-gray-600 mb-1">
                            Scenario {index + 1}: Stop Loss (-$)
                          </label>
                          <input
                            id={`stop-loss-${scenario.id}`}
                            type="number"
                            step="0.01"
                            min="0.01"
                            value={scenario.stopLossOffset}
                            onChange={(e) => updateRiskScenario(scenario.id, 'stopLossOffset', parseFloat(e.target.value) || 0.50)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                          />
                        </div>
                      </div>
                      {riskScenarios.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeRiskScenario(scenario.id)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 transition-colors flex items-center justify-center"
                          title="Remove scenario"
                        >
                          ×
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>

          {formType === 'options' && (
            <div className="pt-3 sm:pt-4 border-t border-gray-200">
              <h2 className="text-sm font-semibold text-gray-600 mb-2">Generated Symbol:</h2>
              <code className="block text-sm sm:text-base bg-gray-100 p-2 sm:p-3 rounded-md text-blue-600 font-mono break-all">
                {formatted || '(fill out all fields)'}
              </code>
            </div>
          )}
          <button
            type="button"
            onClick={handleFetch}
            className="w-full px-4 py-3 bg-blue-600 text-white text-base font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              formType === 'options'
                ? (!formatted || isLoading)
                : (!stockSymbol || !stockDate || isLoading)
            }
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <span className="animate-spin mr-2">⏳</span>
                Loading...
              </span>
            ) : (
              formType === 'options' ? 'Fetch Historical Bars' : 'Fetch 5min Chart'
            )}
          </button>

          {/* Call Option Analysis Results */}
          {formType === 'stocks' && optionAnalysisResults.length > 0 && (
            <div className="mt-4 space-y-4">
              <h3 className="text-sm font-semibold text-blue-800 mb-2">Call Option Analysis Results</h3>
              {optionAnalysisResults.map((result) => (
                <div key={result.id} className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="text-sm font-semibold text-blue-800 mb-2">
                    Breakout #{result.breakoutNumber} - {result.breakoutBarTime}
                  </h4>
                  <div className="space-y-1 text-sm text-blue-700">
                    <div>
                      <span className="font-medium">Strike Price:</span> ${result.strikePrice}
                    </div>
                    <div>
                      <span className="font-medium">Option Open Price:</span> ${result.optionOpenPrice.toFixed(2)}
                    </div>
                    <div>
                      <span className="font-medium">Option MFE:</span> ${result.optionMFE.toFixed(2)}
                    </div>
                    <div>
                      <span className="font-medium">Option Close Price:</span> ${result.optionClosePrice.toFixed(2)}
                    </div>

                    {/* Dynamic Risk Management Results */}
                    <div className="mt-3 space-y-2">
                      <h5 className="text-xs font-semibold text-blue-800">Risk Management Results</h5>

                      {result.riskScenarios.map((scenario, index) => (
                        <div key={scenario.id} className="flex justify-between items-center text-xs">
                          <span className="text-gray-600">
                            Scenario {index + 1} (+${scenario.takeProfitOffset.toFixed(2)}/-${scenario.stopLossOffset.toFixed(2)}):
                          </span>
                          <span className={`font-semibold ${scenario.takeProfitAchieved ? 'text-green-600' : 'text-red-600'}`}>
                            {scenario.takeProfitAchieved ? 'Yes' : 'No'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Put Option Analysis Results */}
          {formType === 'stocks' && putOptionAnalysisResults.length > 0 && (
            <div className="mt-4 space-y-4">
              <h3 className="text-sm font-semibold text-red-800 mb-2">Put Option Analysis Results</h3>
              {putOptionAnalysisResults.map((result) => (
                <div key={result.id} className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="text-sm font-semibold text-red-800 mb-2">
                    Breakout #{result.breakoutNumber} - {result.breakoutBarTime}
                  </h4>
                  <div className="space-y-1 text-sm text-red-700">
                    <div>
                      <span className="font-medium">Strike Price:</span> ${result.strikePrice}
                    </div>
                    <div>
                      <span className="font-medium">Option Open Price:</span> ${result.optionOpenPrice.toFixed(2)}
                    </div>
                    <div>
                      <span className="font-medium">Option MFE:</span> ${result.optionMFE.toFixed(2)}
                    </div>
                    <div>
                      <span className="font-medium">Option Close Price:</span> ${result.optionClosePrice.toFixed(2)}
                    </div>

                    {/* Dynamic Risk Management Results */}
                    <div className="mt-3 space-y-2">
                      <h5 className="text-xs font-semibold text-red-800">Risk Management Results</h5>

                      {result.riskScenarios.map((scenario, index) => (
                        <div key={scenario.id} className="flex justify-between items-center text-xs">
                          <span className="text-gray-600">
                            Scenario {index + 1} (+${scenario.takeProfitOffset.toFixed(2)}/-${scenario.stopLossOffset.toFixed(2)}):
                          </span>
                          <span className={`font-semibold ${scenario.takeProfitAchieved ? 'text-green-600' : 'text-red-600'}`}>
                            {scenario.takeProfitAchieved ? 'Yes' : 'No'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        </div>
      )}
    </main>
  );
}
